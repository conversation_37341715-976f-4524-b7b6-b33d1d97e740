{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"half\", \"nalgebra\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 421182060080001780, "deps": [[10288871127199797760, "pyo3_build_config", false, 12279797411063398627]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\numpy-ae35c7487c15b042\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}