{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py313\", \"abi3-py314\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"generate-import-lib\"]", "target": 14506753996192664611, "profile": 902425061678724816, "path": 2589292419932977380, "deps": [[4684437522915235464, "libc", false, 9685844023081248841], [5099523288940447918, "build_script_build", false, 17845825012342394585]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-ffi-b03a7e2323864099\\dep-lib-pyo3_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}