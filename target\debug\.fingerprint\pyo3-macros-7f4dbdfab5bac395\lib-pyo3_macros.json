{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"experimental-async\", \"experimental-inspect\", \"multiple-pymethods\"]", "target": 13917622123232857288, "profile": 4882076431712427509, "path": 6293466453592724, "deps": [[3060637413840920116, "proc_macro2", false, 1774568440757176574], [4342566878770968593, "pyo3_macros_backend", false, 12178661077877341843], [4974441333307933176, "syn", false, 9617172547029872208], [17990358020177143287, "quote", false, 17691294558460208638]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-macros-7f4dbdfab5bac395\\dep-lib-pyo3_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}