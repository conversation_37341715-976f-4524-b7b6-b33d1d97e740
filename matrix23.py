from app import app
import MetaTrader5 as mt5
import numpy as np
from numba import njit
import pandas as pd
from datetime import datetime, timedelta
import dash
from dash import html, Input, Output, State
import atexit
import plotly.graph_objects as go
import pytz
import scipy.optimize as sco
import json
import time
from joblib import Parallel, delayed
import os
import psutil
from itertools import combinations
import logging # Import logging module
import signal
import sys
import threading
import gc

from ratio_calcs_rust_wrapper import portfolio_variance
from func_mt5 import calculate_returns, fetch_data, convert_log_to_arithmetic_returns, connect_mt5
from func_rest import cached_process_combo, get_optimal_cores, timing_decorator
from func_gfx import create_combined_mpt_string
from func_portfolio import generate_portfolio_suggestions, find_recommended_portfolios, combine_portfolios
from annualization_utils import annualize_candidate_list, annualize_frontier_data, get_timeframe_description, format_return_percentage, format_risk_percentage
from weekend_utils import should_freeze_updates, should_use_friday_data, cache_friday_data, get_cached_friday_data, log_weekend_status
#from market_phase import generate_market_phase_figures # <-- Import the new function

# Import memory management system
try:
    from memory_manager import get_memory_manager, initialize_memory_management
    _use_memory_management = True
    print("Memory management system loaded successfully")
except ImportError as e:
    print(f"Warning: Could not load memory management system: {e}")
    _use_memory_management = False

import layout
import mpt_tracker
import port_table
import mpt_allocation
import mpt_recommendation
import mpt_trading_callbacks  # Import trading callbacks
import portfolio_callbacks  # Import portfolio persistence callbacks
import basket_analysis  # Import basket analysis functionality
#import matrix_phases

app.layout = layout.layout # Assign layout after all imports

# --- MetaTrader5 Initialization ---
# Initialize MT5 connection at startup
if not connect_mt5():
    print("WARNING: Failed to initialize MetaTrader5 connection!")
    print("Please ensure:")
    print("1. MetaTrader5 terminal is running")
    print("2. You are logged into a broker account")
    print("3. 'Allow Algo Trading' is enabled in terminal settings")
else:
    print("MetaTrader5 connection initialized successfully")

# Global variables for graceful shutdown
_shutdown_requested = False
_active_parallel_jobs = []
_shutdown_lock = threading.Lock()

def signal_handler(signum, frame):
    """Handle interrupt signals gracefully"""
    global _shutdown_requested

    with _shutdown_lock:
        if _shutdown_requested:
            print("\nForced shutdown requested. Terminating immediately...")
            sys.exit(1)

        _shutdown_requested = True
        print(f"\nShutdown signal received (signal {signum}). Initiating graceful shutdown...")
        print("Press Ctrl+C again to force immediate termination.")

        # Cancel active parallel jobs
        for job in _active_parallel_jobs:
            try:
                if hasattr(job, '_pool') and job._pool is not None:
                    print("Terminating parallel job pool...")
                    job._pool.terminate()
                    job._pool.join(timeout=5)
            except Exception as e:
                print(f"Error terminating parallel job: {e}")

        # Clear pandas caches to prevent export errors
        try:
            gc.collect()
            print("Memory cleanup completed")
        except Exception as e:
            print(f"Error during memory cleanup: {e}")

        # Cleanup MT5 connection
        cleanup_mt5()

        print("Graceful shutdown completed.")
        sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
if hasattr(signal, 'SIGTERM'):
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal

# Register cleanup function to close MT5 connection on exit
def cleanup_mt5():
    """Clean up MetaTrader5 connection on application exit"""
    try:
        mt5.shutdown()
        print("MetaTrader5 connection closed")
    except Exception as e:
        print(f"Error closing MetaTrader5 connection: {e}")

atexit.register(cleanup_mt5)

def calculate_time_range_for_period_mpt(hours: int):
    """
    Calculate start and end times for MPT periods using matrix_QP-style logic

    Args:
        hours: Number of hours for the period (24, 72, 120, 240)

    Returns:
        Tuple of (start_time, end_time) in Europe/Bucharest timezone
    """
    MARKET_TIMEZONE = pytz.timezone('Europe/Bucharest')
    now = datetime.now(MARKET_TIMEZONE)

    # Calculate end time (current time)
    end_time = now

    # Handle different time periods
    if hours <= 24:
        # For periods <= 24h: from 00:00 today (current behavior)
        if now.weekday() >= 5:  # Weekend
            # Use Friday's data
            days_back = now.weekday() - 4  # Days back to Friday
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=days_back)
            end_time = start_time.replace(hour=23, minute=59, second=59)
        else:
            # Weekday: from 00:00 today
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
    else:
        # For periods > 24h: previous X weekdays (excluding weekends) + current day
        days = hours // 24

        # Start from current day and go back, counting only weekdays
        current_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
        weekdays_needed = days

        # If today is weekend, start from Friday
        if current_date.weekday() >= 5:  # Weekend
            days_to_friday = current_date.weekday() - 4
            current_date = current_date - timedelta(days=days_to_friday)
            end_time = current_date.replace(hour=23, minute=59, second=59)

        # Count back weekdays only
        weekdays_counted = 0
        check_date = current_date

        while weekdays_counted < weekdays_needed:
            check_date = check_date - timedelta(days=1)
            # Only count weekdays (Monday=0 to Friday=4)
            if check_date.weekday() < 5:
                weekdays_counted += 1

        start_time = check_date

        print(f"Multi-day period {hours}h: excluding weekends, using {weekdays_needed} weekdays")

    print(f"Time period {hours}h: {start_time} to {end_time}")
    print(f"Matrix_QP-style calculation: Weekend={now.weekday() >= 5}, Hours={hours}")
    return start_time, end_time

# --- Basic Logging Configuration ---
# Configure logging to output INFO level messages to the console
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__) # Optional: Get a logger for this specific module if needed

# Process chunks with controlled parallelism and CPU throttling
def get_adaptive_n_jobs():
    """Dynamically determine optimal number of cores based on current load and system resources"""
    import multiprocessing

    # Get system information
    max_cores = multiprocessing.cpu_count()
    current_cpu = psutil.cpu_percent(interval=0.1)
    memory = psutil.virtual_memory()
    available_memory_gb = memory.available / (1024**3)  # Convert to GB

    # Conservative core allocation based on CPU load
    if current_cpu > 85:
        cores_by_cpu = max(1, max_cores // 4)  # Use 25% of cores when very high
    elif current_cpu > 70:
        cores_by_cpu = max(2, max_cores // 3)  # Use 33% of cores when high
    elif current_cpu > 50:
        cores_by_cpu = max(2, max_cores // 2)  # Use 50% of cores when moderate
    else:
        cores_by_cpu = max(2, max_cores - 2)   # Leave 2 cores for system when normal

    # Memory-based core limitation (each core needs ~0.5GB for portfolio optimization)
    cores_by_memory = max(1, int(available_memory_gb / 0.5))

    # Use the more conservative limit
    optimal_cores = min(cores_by_cpu, cores_by_memory, max_cores - 1)

    print(f"System resources: CPU={current_cpu:.1f}%, Memory={available_memory_gb:.1f}GB available")
    print(f"Core allocation: CPU-limited={cores_by_cpu}, Memory-limited={cores_by_memory}, Selected={optimal_cores}")

    return optimal_cores
        
# --- Parallel Processing Configuration ---
# Create new Parallel instances for each operation to avoid conflicts
def create_parallel_executor(n_jobs=None):
    """Create a new Parallel instance for each operation to avoid conflicts"""
    global _shutdown_requested, _active_parallel_jobs

    # Check if shutdown was requested
    if _shutdown_requested:
        raise InterruptedError("Shutdown requested, cancelling parallel execution")

    if n_jobs is None:
        n_jobs = get_adaptive_n_jobs()

    parallel_job = Parallel(
        n_jobs=n_jobs,
        verbose=10,
        prefer="processes",
        backend="loky",
        batch_size="auto",
        max_nbytes='10M'
    )

    # Track active parallel jobs for cleanup
    with _shutdown_lock:
        _active_parallel_jobs.append(parallel_job)

    return parallel_job

def safe_parallel_execute(parallel_job, delayed_tasks):
    """Execute parallel tasks with proper cleanup and error handling"""
    global _shutdown_requested, _active_parallel_jobs

    try:
        # Check if shutdown was requested before starting
        if _shutdown_requested:
            raise InterruptedError("Shutdown requested, cancelling execution")

        # Execute the parallel tasks
        results = parallel_job(delayed_tasks)

        return results

    except (KeyboardInterrupt, InterruptedError) as e:
        print(f"Parallel execution interrupted: {e}")
        # Force cleanup of the parallel job
        try:
            if hasattr(parallel_job, '_pool') and parallel_job._pool is not None:
                parallel_job._pool.terminate()
                parallel_job._pool.join(timeout=2)
        except Exception as cleanup_error:
            print(f"Error during parallel job cleanup: {cleanup_error}")
        raise

    finally:
        # Remove from active jobs list
        with _shutdown_lock:
            if parallel_job in _active_parallel_jobs:
                _active_parallel_jobs.remove(parallel_job)


##################################################
#---MAIN (CACHE - MEMORY - CPU - TIMING)---#
# Set process priority to below normal to allow other system processes to run
try:
    process = psutil.Process(os.getpid())
    if os.name == 'nt':  # Windows
        process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
    else:  # Unix-based
        process.nice(10)  # Higher nice value = lower priority
except Exception as e:
    print(f"Error getting psutil process: {e}")
    pass

@app.callback(
    [Output('portfolio-table', 'children'),
     Output('efficient-frontier', 'figure'),
     Output('portfolio-returns', 'figure'),
     Output('optimization-running-store', 'data'),
     Output('combined-mpt-weights', 'value'),
     Output('cvar-frontier', 'figure'),
     Output('recommended-portfolios-store', 'data'),
     Output('recommended-cvar-portfolios-store', 'data'),
     Output('clicked-portfolio-store', 'data')],  # Added Output for the new store
    [Input('interval-optim', 'n_intervals'),
     Input('efficient-frontier', 'clickData'),
     Input('cvar-frontier', 'clickData'),
     Input('frontier-option', 'value'),
     Input('annotation-total-weight', 'value'),
     Input('annualize-returns-toggle', 'value')],
    [State('optimization-running-store', 'data'),
     State('clicked-portfolio-store', 'data')]  # Added State for the new store
)
@timing_decorator
def update_portfolio_optimization(n_intervals, ef_clickData, cvar_clickData, frontier_option, annotation_total_weight, annualize_toggle, optimization_running, stored_click_data):
    global _shutdown_requested

    # Check if shutdown was requested
    if _shutdown_requested:
        print("Shutdown requested, skipping optimization")
        empty_fig = go.Figure()
        empty_fig.update_layout(template="plotly_dark")
        return ("Shutdown requested", empty_fig, empty_fig, False, "", empty_fig, None, None, None)

    # Log weekend status for debugging
    log_weekend_status()

    # Determine if annualization should be applied
    annualize = 'annualize' in (annualize_toggle or [])
    print(f"Annualization toggle: {annualize_toggle}, Annualize: {annualize}")

    # Initialize memory management
    memory_manager = None
    if _use_memory_management:
        try:
            memory_manager = initialize_memory_management()
            print(f"Memory management active - Current usage: {memory_manager.get_current_stats().usage_percent:.1f}%")
        except Exception as e:
            print(f"Error initializing memory management: {e}")
            memory_manager = None

    # Get the trigger ID to understand what triggered this callback
    trigger_id = None
    if dash.callback_context.triggered:
        trigger_id = dash.callback_context.triggered[0]['prop_id'].split('.')[0]

    # Check if we should use Friday data during weekend
    cache_key = f"portfolio_optimization_{frontier_option}_{annotation_total_weight}"

    if should_use_friday_data():
        # During weekends, only run optimization if triggered by click, not by timer
        if trigger_id == 'interval-optim' and not stored_click_data:
            print("Weekend mode: Preventing timer-triggered optimization, no stored click data")
            raise dash.exceptions.PreventUpdate

        # Try to get cached Friday data
        cached_data = get_cached_friday_data(cache_key)
        if cached_data is not None:
            print("Weekend mode: Using cached Friday portfolio optimization data")
            return cached_data
        else:
            print("Weekend mode: No cached data available, using Friday data with current parameters")

    # If optimization is already running, prevent update
    if optimization_running:
        raise dash.exceptions.PreventUpdate

    # Determine what triggered the callback
    ctx = dash.callback_context
    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else 'interval-optim' # Default to timer if no trigger

    # If timer triggered AND optimization is running, skip this update
    if trigger_id == 'interval-optim' and optimization_running:
        # Return the previous values unchanged + True to indicate still running
        raise dash.exceptions.PreventUpdate

    # --- Configuration ---
    #checks_per_pair = 50  # Minimum occurrences for a pair in size 3, 4, 5 combos
    
    combined_mpt_string = ""
    suggested_portfolios = []
    clicked_portfolio_data_to_store = None # Initialize store output
    # Initialize empty values to handle early returns
    empty_fig = go.Figure()
    empty_fig.update_layout(template="plotly_dark") # Ensure empty figs use dark theme
    
    # Determine clickData based on trigger or stored data
    clickData = None
    if trigger_id == 'efficient-frontier' and ef_clickData and 'points' in ef_clickData and ef_clickData['points']:
        clickData = ef_clickData
        # Store the customdata from the click
        try:
            clicked_portfolio_data_to_store = json.loads(ef_clickData['points'][0]['customdata'])
            # Add flag indicating which frontier was clicked
            clicked_portfolio_data_to_store['frontier_type'] = 'efficient-frontier'
        except Exception as e:
            print(f"Error storing EF click data: {e}")
            clicked_portfolio_data_to_store = None
    elif trigger_id == 'cvar-frontier' and cvar_clickData and 'points' in cvar_clickData and cvar_clickData['points']:
        clickData = cvar_clickData
        # Store the customdata from the click
        try:
            clicked_portfolio_data_to_store = json.loads(cvar_clickData['points'][0]['customdata'])
             # Add flag indicating which frontier was clicked
            clicked_portfolio_data_to_store['frontier_type'] = 'cvar-frontier'
        except Exception as e:
            print(f"Error storing CVaR click data: {e}")
            clicked_portfolio_data_to_store = None
    elif trigger_id == 'interval-optim' and stored_click_data:
        # If timer triggered, use the stored click data
        print("Timer triggered, using stored click data.")
        clickData = {'points': [{'customdata': json.dumps(stored_click_data)}]} # Reconstruct clickData format
        # Keep the stored data as is for the next interval
        clicked_portfolio_data_to_store = stored_click_data
    else:
        # No valid click or stored data, clear the store
        clicked_portfolio_data_to_store = None
    
    # Set optimization as running
    optimization_running = True
    
    try:
        current_usage = psutil.cpu_percent(interval=0.5)
        if current_usage > 70:
            time.sleep(1.5)  # Add delay if CPU is already high

        # Define the 28 symbols.
        symbols = [
            "EURUSD", "EURGBP", "EURAUD", "EURNZD", "EURCHF", "EURCAD",
            "GBPUSD", "GBPAUD", "GBPNZD", "GBPCHF", "GBPCAD",
            "AUDUSD", "AUDNZD", "AUDCHF", "AUDCAD",
            "NZDUSD", "NZDCHF", "NZDCAD",
            "USDCHF", "USDCAD",
            "CADCHF",
            "EURJPY", "GBPJPY", "AUDJPY", "NZDJPY", "CADJPY", "CHFJPY", "USDJPY"
            #"XAUUSD"
        ]

        def calculate_business_hours(required_hours):
            """Calculate how many calendar hours to fetch to get the required business hours"""
            now = datetime.now(pytz.timezone('Europe/Bucharest'))
            current_day = now.weekday()  # 0 = Monday, 6 = Sunday
            
            hours_needed = required_hours + 3
            total_hours = 0
            current_hour = now.hour
            
            # Start from the current time and work backward
            day_pointer = current_day
            hour_pointer = current_hour
            
            while hours_needed > 0:
                # If we're on a weekday (0-4)
                if day_pointer < 5:
                    # Calculate hours available in this day
                    hours_available = hour_pointer if day_pointer == current_day else 24
                    hours_to_take = min(hours_needed, hours_available)
                    
                    hours_needed -= hours_to_take
                    total_hours += hours_to_take
                else:
                    # Weekend day - add to total hours without reducing hours_needed
                    total_hours += 24
                
                # Move to previous day
                hour_pointer = 24  # Reset for all days except the first
                day_pointer = (day_pointer - 1) % 7
            
            return total_hours

        # Choose market data based on the frontier-option radio button.
        # MODIFIED: We'll now fetch 3x the data needed for each option
        if frontier_option == 'today':
            # Use matrix_QP-style 24h time handling pattern
            now = datetime.now(pytz.timezone('Europe/Bucharest'))

            # If it's a weekend, use full Friday data (00:00 to 23:59) like matrix_QP 24h pattern
            if now.weekday() >= 5:  # weekend check
                from weekend_utils import get_last_friday_end
                friday_end = get_last_friday_end()
                # Use full Friday data from 00:00 to 23:59 (consistent with matrix_QP)
                friday_start = friday_end.replace(hour=0, minute=0, second=0, microsecond=0)
                print(f"Weekend mode: Using full Friday data from {friday_start} to {friday_end}")
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                                       start_time=friday_start, end_time=friday_end)
                business_hours_window = 24  # Use full 24 hours for display
            else:
                # During weekdays, use current day from 00:00 to now (like matrix_QP 24h pattern)
                start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
                print(f"Weekday mode: Using current day data from {start_of_day} to {now}")
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                                       start_time=start_of_day, end_time=now)
                hours_current = (now - start_of_day).total_seconds() / 3600
                business_hours_window = hours_current  # Keep current window for display
            
        elif frontier_option == '240':
            # Use matrix_QP-style time calculation for 240 hours (10 weekdays)
            start_time, end_time = calculate_time_range_for_period_mpt(240)
            print(f"240h mode: Using matrix_QP-style time range from {start_time} to {end_time}")
            market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                                   start_time=start_time, end_time=end_time)
            business_hours_window = 240  # Keep original window for display

        elif frontier_option == '120':
            # Use matrix_QP-style time calculation for 120 hours (5 weekdays)
            start_time, end_time = calculate_time_range_for_period_mpt(120)
            print(f"120h mode: Using matrix_QP-style time range from {start_time} to {end_time}")
            market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                                   start_time=start_time, end_time=end_time)
            business_hours_window = 120  # Keep original window for display

        elif frontier_option == '72':
            # Use matrix_QP-style time calculation for 72 hours (3 weekdays)
            start_time, end_time = calculate_time_range_for_period_mpt(72)
            print(f"72h mode: Using matrix_QP-style time range from {start_time} to {end_time}")
            market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                                   start_time=start_time, end_time=end_time)
            business_hours_window = 72  # Keep original window for display

        else:
            # For 24-hour option - use matrix_QP-style time calculation
            start_time, end_time = calculate_time_range_for_period_mpt(24)
            print(f"24h mode: Using matrix_QP-style time range from {start_time} to {end_time}")
            market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                                   start_time=start_time, end_time=end_time)
            business_hours_window = 24  # Keep original window for display

        # Calculate returns for all cases - this is the full dataset for statistics
        full_returns_df = calculate_returns(market_data)
        
        # Make a copy for display purposes
        returns_df = full_returns_df.copy()

        # Filter the display data based on business hours window
        if not returns_df.empty:
            # Filter out non-business days (weekends) from both datasets
            business_days_mask = returns_df.index.dayofweek < 5
            returns_df = returns_df[business_days_mask]
            full_returns_df = full_returns_df[business_days_mask]
            
            # Special case for 24-hour option and "today" option during weekends
            if frontier_option == '24' or (frontier_option == 'today' and now.weekday() >= 5):
                # Count how many business hours we have, working backward from the most recent data
                if len(returns_df) > 0:
                    # Get the most recent timestamp
                    #latest_timestamp = returns_df.index[-1]
                    business_rows_needed = 24 * 4  # 4 rows per hour for M15

                    # Take exactly the number of rows that correspond to 24 business hours for display
                    if len(returns_df) > business_rows_needed:
                        returns_df = returns_df.tail(business_rows_needed)
            else:
                # For other options, calculate how many rows we need for the display window
                rows_per_hour = 4  # For M15 timeframe
                business_rows_needed = int(business_hours_window * rows_per_hour)
                
                # Filter the display dataset to the requested window size
                if len(returns_df) > business_rows_needed:
                    returns_df = returns_df.tail(business_rows_needed)

        # Continue with the empty check
        if full_returns_df.empty:
            # Return empty figures for MPT/CVaR plots
            return ("No Data", empty_fig, empty_fig, False, "", empty_fig, None, None, None) # MPT/CVaR outputs (9 total)

        # Prepare lists to hold candidate portfolios.
        all_portfolios_minvar = []
        all_portfolios_maxsharpe = []
        all_portfolios_maxsortino = []
        all_portfolios_maxomega = []
        all_portfolios_maxcalmar = []
        all_portfolios_maxmodsharpe = []
        all_candidates_composite = []  # For high sharpe & low minvar portfolios.
        ss_candidates_composite = []  # For high sharpe & low minvar portfolios.

        # Counters and limits.
        #counter = 0
        max_combos = 100000  # increased iterations
        #checks_per_pair = 50 #how many pairs for limit / for triples quadraples, etc
        matches = { 3, 4 } #how many pairs for limit / for triples quadraples, etc
        # These dictionaries record per-symbol occurrences in each table.
        #occurrence_minvar = {}
        #occurrence_maxsharpe = {}
        #occurrence_maxsortino = {}
        #occurrence_maxomega = {}
        #occurrence_maxcalmar = {}
        #occurrence_maxmodsharpe = {}
        #occurrence_bestcomp = {}
        #occurrence_bestmaxss = {}

        # Loop over combination sizes 3 to 5.
        start_time = datetime.now()
        
        print(f"Starting portfolio optimization with {len(symbols)} symbols...")
        # Add this near your other parameters
        correlation_max_threshold = 0.5
        correlation_min_threshold = -0.5

        # After defining other parameters like correlation thresholds
        print(f"Starting portfolio optimization with {len(symbols)} symbols...")
        print(f"Precomputing correlation matrix for {len(symbols)} symbols...")
        full_corr_matrix = full_returns_df[symbols].corr()

        # --- Market Phase Analysis is now handled by a separate callback ---

        # Define separate functions for each combination size
        @njit
        def check_valid_combinations_of_size_3(all_correlations, max_corr, min_corr):
            """Check valid combinations of size 3"""
            valid_indices = []
            n_symbols = all_correlations.shape[0]
            
            for i in range(n_symbols):
                for j in range(i+1, n_symbols):
                    for k in range(j+1, n_symbols):
                        # Check all pairwise correlations
                        corr12 = all_correlations[i, j]
                        corr13 = all_correlations[i, k]
                        corr23 = all_correlations[j, k]
                        
                        if (min_corr <= corr12 <= max_corr and 
                            min_corr <= corr13 <= max_corr and 
                            min_corr <= corr23 <= max_corr):
                            valid_indices.append((i, j, k))
            
            return valid_indices

        @njit
        def check_valid_combinations_of_size_4(all_correlations, max_corr, min_corr):
            """Check valid combinations of size 4"""
            valid_indices = []
            n_symbols = all_correlations.shape[0]
            
            for i in range(n_symbols):
                for j in range(i+1, n_symbols):
                    for k in range(j+1, n_symbols):
                        for l in range(k+1, n_symbols):
                            # Check all pairwise correlations (6 pairs)
                            corr12 = all_correlations[i, j]
                            corr13 = all_correlations[i, k]
                            corr14 = all_correlations[i, l]
                            corr23 = all_correlations[j, k]
                            corr24 = all_correlations[j, l]
                            corr34 = all_correlations[k, l]
                            
                            if (min_corr <= corr12 <= max_corr and 
                                min_corr <= corr13 <= max_corr and
                                min_corr <= corr14 <= max_corr and
                                min_corr <= corr23 <= max_corr and
                                min_corr <= corr24 <= max_corr and
                                min_corr <= corr34 <= max_corr):
                                valid_indices.append((i, j, k, l))
            
            return valid_indices

        @njit
        def check_valid_combinations_of_size_5(all_correlations, max_corr, min_corr):
            """Check valid combinations of size 5"""
            valid_indices = []
            n_symbols = all_correlations.shape[0]
            
            for i in range(n_symbols):
                for j in range(i+1, n_symbols):
                    for k in range(j+1, n_symbols):
                        for l in range(k+1, n_symbols):
                            for m in range(l+1, n_symbols):
                                # Check all 10 pairwise correlations
                                corr12 = all_correlations[i, j]
                                corr13 = all_correlations[i, k]
                                corr14 = all_correlations[i, l]
                                corr15 = all_correlations[i, m]
                                corr23 = all_correlations[j, k]
                                corr24 = all_correlations[j, l]
                                corr25 = all_correlations[j, m]
                                corr34 = all_correlations[k, l]
                                corr35 = all_correlations[k, m]
                                corr45 = all_correlations[l, m]
                                
                                if (min_corr <= corr12 <= max_corr and 
                                    min_corr <= corr13 <= max_corr and
                                    min_corr <= corr14 <= max_corr and
                                    min_corr <= corr15 <= max_corr and
                                    min_corr <= corr23 <= max_corr and
                                    min_corr <= corr24 <= max_corr and
                                    min_corr <= corr25 <= max_corr and
                                    min_corr <= corr34 <= max_corr and
                                    min_corr <= corr35 <= max_corr and
                                    min_corr <= corr45 <= max_corr):
                                    valid_indices.append((i, j, k, l, m))
            
            return valid_indices

        @njit
        def check_valid_combinations_of_size_6(all_correlations, max_corr, min_corr):
            """Check valid combinations of size 6"""
            valid_indices = []
            n_symbols = all_correlations.shape[0]
            
            for i in range(n_symbols):
                for j in range(i+1, n_symbols):
                    for k in range(j+1, n_symbols):
                        for l in range(k+1, n_symbols):
                            for m in range(l+1, n_symbols):
                                for n in range(m+1, n_symbols):
                                    # Check all 15 pairwise correlations for 6 symbols
                                    corr12 = all_correlations[i, j]
                                    corr13 = all_correlations[i, k]
                                    corr14 = all_correlations[i, l]
                                    corr15 = all_correlations[i, m]
                                    corr16 = all_correlations[i, n]
                                    corr23 = all_correlations[j, k]
                                    corr24 = all_correlations[j, l]
                                    corr25 = all_correlations[j, m]
                                    corr26 = all_correlations[j, n]
                                    corr34 = all_correlations[k, l]
                                    corr35 = all_correlations[k, m]
                                    corr36 = all_correlations[k, n]
                                    corr45 = all_correlations[l, m]
                                    corr46 = all_correlations[l, n]
                                    corr56 = all_correlations[m, n]
                                    
                                    if (min_corr <= corr12 <= max_corr and 
                                        min_corr <= corr13 <= max_corr and
                                        min_corr <= corr14 <= max_corr and
                                        min_corr <= corr15 <= max_corr and
                                        min_corr <= corr16 <= max_corr and
                                        min_corr <= corr23 <= max_corr and
                                        min_corr <= corr24 <= max_corr and
                                        min_corr <= corr25 <= max_corr and
                                        min_corr <= corr26 <= max_corr and
                                        min_corr <= corr34 <= max_corr and
                                        min_corr <= corr35 <= max_corr and
                                        min_corr <= corr36 <= max_corr and
                                        min_corr <= corr45 <= max_corr and
                                        min_corr <= corr46 <= max_corr and
                                        min_corr <= corr56 <= max_corr):
                                        valid_indices.append((i, j, k, l, m, n))
            
            return valid_indices

        # Convert correlation matrix to numpy array
        corr_array = full_corr_matrix.values
        symbols_array = np.array(symbols)

        # Find valid combinations of different sizes
        valid_combos = []
        for n in matches:
            print(f"Finding valid combinations of size {n}...")
            
            # Use the appropriate function based on the size
            if n == 3:
                indices = check_valid_combinations_of_size_3(corr_array, correlation_max_threshold, correlation_min_threshold)
            elif n == 4:
                indices = check_valid_combinations_of_size_4(corr_array, correlation_max_threshold, correlation_min_threshold)
            elif n == 5:
                indices = check_valid_combinations_of_size_5(corr_array, correlation_max_threshold, correlation_min_threshold)
            elif n == 6:
                indices = check_valid_combinations_of_size_6(corr_array, correlation_max_threshold, correlation_min_threshold)
            else:
                print(f"Size {n} not supported")
                indices = []
            
            # Convert indices back to symbol combinations
            for idx_tuple in indices:
                combo = tuple(str(symbols_array[i]) for i in idx_tuple) # Convert numpy strings to python strings
                valid_combos.append(combo)
            
            print(f"Found {len(indices)} valid combinations of size {n}")

        print(f"Found {len(valid_combos)} valid combinations to process in total")

        # Now continue with your existing code for processing these combinations        
        print(f"Found {len(valid_combos)} valid combinations to process")
        if not valid_combos:
            empty_fig = go.Figure()
            empty_fig.update_layout(
                title="No valid combinations found - try relaxing correlation constraints",
                paper_bgcolor='black', 
                plot_bgcolor='black',
                font=dict(color='white')
            )
            # Return empty figures for all plots, including market phase
            return ("No valid combinations found", empty_fig, empty_fig, False, "", empty_fig, None, None, None)#, # MPT/CVaR outputs - empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_fig) # Market Phase outputs
        
        # Process combinations in parallel
        all_results = []
        num_processes = get_optimal_cores(max_usage_percent=80)
        max_combos_to_process = min(max_combos, len(valid_combos))
        
        print(f"Starting parallel processing with {num_processes} cores")
        print(f"Processing up to {max_combos_to_process} combinations")

        # Implement adaptive batch sizing based on system resources
        def get_adaptive_batch_size(max_combos_to_process, num_processes):
            """Calculate optimal batch size based on system load and memory"""
            current_cpu = psutil.cpu_percent(interval=0.1)  # Faster check
            memory = psutil.virtual_memory()
            available_memory_gb = memory.available / (1024**3)

            # Base batch size calculation
            base_batch_size = max(1, max_combos_to_process // (num_processes * 4))

            # Use memory manager if available for intelligent batch sizing
            if memory_manager:
                try:
                    optimal_batch_size = memory_manager.get_recommended_batch_size(
                        base_batch_size,
                        max_size=min(50, max_combos_to_process)
                    )
                    stats = memory_manager.get_current_stats()
                    print(f"Memory-aware batch sizing: Usage={stats.usage_percent:.1f}%, Pressure={memory_manager.get_memory_pressure()}, Batch size={optimal_batch_size}")
                    return optimal_batch_size
                except Exception as e:
                    print(f"Error with memory-aware batch sizing, falling back to standard: {e}")

            # Fallback to original logic if memory manager not available
            # Adjust based on CPU load (smaller batches when CPU is high)
            if current_cpu > 85:
                cpu_factor = 0.3  # Very small batches
            elif current_cpu > 70:
                cpu_factor = 0.5  # Small batches
            elif current_cpu > 50:
                cpu_factor = 0.7  # Medium batches
            else:
                cpu_factor = 1.0  # Normal batches

            # Adjust based on available memory (smaller batches when memory is low)
            if available_memory_gb < 1.0:
                memory_factor = 0.3  # Very small batches
            elif available_memory_gb < 2.0:
                memory_factor = 0.5  # Small batches
            elif available_memory_gb < 4.0:
                memory_factor = 0.7  # Medium batches
            else:
                memory_factor = 1.0  # Normal batches

            # Use the most conservative factor
            adjustment_factor = min(cpu_factor, memory_factor)
            optimal_batch_size = max(1, int(base_batch_size * adjustment_factor))

            # Cap at reasonable limits
            optimal_batch_size = min(optimal_batch_size, 50, max_combos_to_process)

            print(f"Standard batch sizing: CPU={current_cpu:.1f}%, Memory={available_memory_gb:.1f}GB, Batch size={optimal_batch_size}")

            return optimal_batch_size

        # Then use it in your code
        chunk_size = get_adaptive_batch_size(max_combos_to_process, num_processes)

        data_chunks = []

        for i in range(0, max_combos_to_process, chunk_size):
            # Check for shutdown request during chunk preparation
            if _shutdown_requested:
                raise InterruptedError("Shutdown requested during chunk preparation")

            chunk_end = min(i + chunk_size, max_combos_to_process)
            # Create data for just this chunk with both datasets
            chunk_data = [(combo, returns_df, full_returns_df) for combo in valid_combos[i:chunk_end]]
            data_chunks.append(chunk_data)

        # Check for shutdown request before starting parallel processing
        if _shutdown_requested:
            raise InterruptedError("Shutdown requested before parallel processing")

        # Process chunks using a new parallel executor to avoid conflicts
        parallel_executor = create_parallel_executor(num_processes)
        delayed_tasks = [delayed(cached_process_combo)(chunk, frontier_option) for chunk in data_chunks]
        batch_results = safe_parallel_execute(parallel_executor, delayed_tasks)

        # Flatten results
        for result_list in batch_results:
            all_results.extend(result_list)
        
        # Organize results by portfolio type
        for result_type, candidate in all_results:
            if result_type == 'minvar':
                all_portfolios_minvar.append(candidate)
            elif result_type == 'maxsharpe':
                all_portfolios_maxsharpe.append(candidate)
            elif result_type == 'maxsortino':
                all_portfolios_maxsortino.append(candidate)
            elif result_type == 'maxomega':
                all_portfolios_maxomega.append(candidate)
            elif result_type == 'maxcalmar':
                all_portfolios_maxcalmar.append(candidate)
            elif result_type == 'maxmodsharpe':
                all_portfolios_maxmodsharpe.append(candidate)
            elif result_type == 'composite':
                all_candidates_composite.append(candidate)
            elif result_type == 'ss_composite':
                ss_candidates_composite.append(candidate)

        # After organizing results by portfolio type, add this code
        # to track the top return combinations
        top_return_candidates = []

        # Track combinations with high returns regardless of ratios
        for result_type, candidate in all_results:
            # Make a copy and add it to our tracking list
            if 'return' in candidate:
                top_return_candidates.append(candidate.copy())

        # Sort all candidates by return (descending)
        sorted_by_return = sorted(top_return_candidates, key=lambda x: x['return'], reverse=True)

        # Keep just the top 10 highest return combinations
        top_return_candidates = sorted_by_return[:10]

        # Add a special label for these
        for cand in top_return_candidates:
            cand['optimization'] = 'Max Return'

        end_time = datetime.now()
        elapsed = (end_time - start_time).total_seconds()
        print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Time elapsed: {elapsed:.2f} seconds.")
        print(f"Generated portfolios: MinVar={len(all_portfolios_minvar)}, MaxSharpe={len(all_portfolios_maxsharpe)}, " +
            f"MaxSortino={len(all_portfolios_maxsortino)}, MaxOmega={len(all_portfolios_maxomega)}, " +
            f"MaxCalmar={len(all_portfolios_maxcalmar)}, MaxModSharpe=", len(all_portfolios_maxmodsharpe))
        
        # Sort candidates.
        sorted_minvar = sorted(all_portfolios_minvar, key=lambda x: x['risk'])
        sorted_maxsharpe = sorted(all_portfolios_maxsharpe, key=lambda x: -x['sharpe'])
        #sorted_maxsharpe.reverse()
        sorted_maxsortino = sorted(all_portfolios_maxsortino, key=lambda x: -x['sortino'])
        #sorted_maxsortino.reverse()
        sorted_maxomega = sorted(all_portfolios_maxomega, key=lambda x: -x['omega'])
        #sorted_maxomega.reverse()
        sorted_maxcalmar = sorted(all_portfolios_maxcalmar, key=lambda x: -x['calmar'])
        #sorted_maxcalmar.reverse()
        sorted_maxmodsharpe = sorted(all_portfolios_maxmodsharpe, key=lambda x: -x['mod_sharpe'])

        # IMPORTANT: Create a separate list for the very best performers that will bypass filtering
        top_performers = []

        # Add the top 5 Sharpe ratio portfolios to guaranteed list
        if sorted_maxsharpe:
            top_sharpe = sorted_maxsharpe[:5]
            for cand in top_sharpe:
                cand_copy = cand.copy()
                cand_copy['optimization'] = 'Top Sharpe'
                top_performers.append(cand_copy)

        # Add the top 5 Sortino ratio portfolios to guaranteed list
        if sorted_maxsortino:
            top_sortino = sorted_maxsortino[:5]
            for cand in top_sortino:
                cand_copy = cand.copy()
                cand_copy['optimization'] = 'Top Sortino'
                top_performers.append(cand_copy)

        # Add the top 5 Omega ratio portfolios to guaranteed list
        if sorted_maxomega:
            top_omega = sorted_maxomega[:5]
            for cand in top_omega:
                cand_copy = cand.copy()
                cand_copy['optimization'] = 'Top Omega'
                top_performers.append(cand_copy)

        # Add the top 5 Calmar ratio portfolios to guaranteed list
        if sorted_maxcalmar:
            top_calmar = sorted_maxcalmar[:5]
            for cand in top_calmar:
                cand_copy = cand.copy()
                cand_copy['optimization'] = 'Top Calmar'
                top_performers.append(cand_copy)

        # Add the top 5 Calmar ratio portfolios to guaranteed list
        if sorted_maxmodsharpe:
            top_modsharpe = sorted_maxmodsharpe[:5]
            for cand in top_modsharpe:
                cand_copy = cand.copy()
                cand_copy['optimization'] = 'Top CF Sharpe'
                top_performers.append(cand_copy)

        # Define size-specific pair occurrence thresholds
        pair_occurrence_thresholds = {
            3: 15,
            4: 15,
            5: 15,
            6: 15
        }
        print(f"Using pair occurrence thresholds: {pair_occurrence_thresholds}")

        def filter_candidates_by_pair_occurrence(candidates, max_occurrences_by_size):
            """
            Filters candidates to enforce a maximum occurrence limit for each pair,
            prioritizing higher-returning candidates.
            
            Args:
                candidates: List of portfolio candidates to filter
                max_occurrences_by_size: Dictionary mapping combo sizes (3,4,5) to maximum 
                                        allowed occurrences of any pair in the filtered results
            
            Returns:
                List of filtered candidates that meet the pair occurrence constraints
            """
            if not candidates:
                return []

            # Sort candidates by 'return' (lowercase) descending to prioritize keeping the best ones
            try:
                sorted_candidates = sorted(candidates, key=lambda x: x.get('return', -float('inf')), reverse=True)
            except TypeError:
                # Fallback if 'return' contains non-numeric data
                print("Warning: Could not sort candidates by 'return'. Filtering without prioritization.")
                sorted_candidates = candidates

            filtered_candidates = []
            accepted_pair_counts = {} # Tracks counts of pairs in the filtered list

            for cand in sorted_candidates:
                combo = cand.get('combo', [])
                combo_size = len(combo)

                # Keep candidates not of size 3, 4, or 5 unconditionally
                if combo_size not in max_occurrences_by_size:
                    filtered_candidates.append(cand)
                    continue

                # Check if adding this candidate exceeds any pair limits
                base_symbols = [sym.lstrip('-') for sym in combo]
                can_add = True
                pairs_in_combo = []
                if not base_symbols or len(base_symbols) < 2:
                    can_add = False # Cannot form pairs from empty or single-item combos
                else:
                    max_allowed = max_occurrences_by_size[combo_size]
                    for pair in combinations(base_symbols, 2):
                        sorted_pair = tuple(sorted(pair))
                        pairs_in_combo.append(sorted_pair)
                        if accepted_pair_counts.get(sorted_pair, 0) >= max_allowed:
                            can_add = False
                            break # Exceeds limit for this pair

                # If allowed, add to list and update counts
                if can_add:
                    filtered_candidates.append(cand)
                    for pair in pairs_in_combo:
                        accepted_pair_counts[pair] = accepted_pair_counts.get(pair, 0) + 1

            print(f"Filtered {len(candidates)} -> {len(filtered_candidates)} candidates.")
            return filtered_candidates
        
        # Apply the new pair-based filtering
        print("Applying pair occurrence filter...")
        best_minvar = filter_candidates_by_pair_occurrence(sorted_minvar, pair_occurrence_thresholds)
        best_minvar = best_minvar[:20]
        best_maxsharpe = filter_candidates_by_pair_occurrence(sorted_maxsharpe, pair_occurrence_thresholds)
        best_maxsharpe = best_maxsharpe[:2000]
        best_maxsortino = filter_candidates_by_pair_occurrence(sorted_maxsortino, pair_occurrence_thresholds)
        best_maxsortino = best_maxsortino[:2000]
        best_maxcalmar = filter_candidates_by_pair_occurrence(sorted_maxcalmar, pair_occurrence_thresholds)
        best_maxcalmar = best_maxcalmar[:2000]
        best_maxmodsharpe = filter_candidates_by_pair_occurrence(sorted_maxmodsharpe, pair_occurrence_thresholds)
        best_maxmodsharpe = best_maxmodsharpe[:2000]
        best_maxomega = filter_candidates_by_pair_occurrence(sorted_maxomega, pair_occurrence_thresholds)
        best_maxomega = best_maxomega[:2000]

        # Apply annualization to all candidate groups if requested
        if annualize:
            print("Applying annualization to portfolio candidates...")
            best_minvar = annualize_candidate_list(best_minvar, mt5.TIMEFRAME_M15, annualize=True)
            best_maxsharpe = annualize_candidate_list(best_maxsharpe, mt5.TIMEFRAME_M15, annualize=True)
            best_maxsortino = annualize_candidate_list(best_maxsortino, mt5.TIMEFRAME_M15, annualize=True)
            best_maxcalmar = annualize_candidate_list(best_maxcalmar, mt5.TIMEFRAME_M15, annualize=True)
            best_maxomega = annualize_candidate_list(best_maxomega, mt5.TIMEFRAME_M15, annualize=True)
            best_maxmodsharpe = annualize_candidate_list(best_maxmodsharpe, mt5.TIMEFRAME_M15, annualize=True)

        # Note: The 'required_count' logic from the old filter is removed.
        # If you need to limit the number of results per category, that logic needs to be added separately after filtering.
        for cand in best_maxomega:
            cand['optimization'] = 'Max Omega'

        # Composite candidates: low min-var and high Sharpe.
        composite_candidates = [cand for cand in all_candidates_composite if 'sharpe' in cand]
        composite_sorted = sorted(composite_candidates, key=lambda x: x['sharpe']/(x['risk']+1e-15), reverse=True)
        best_composite = filter_candidates_by_pair_occurrence(composite_sorted, pair_occurrence_thresholds)
        best_composite = best_composite[:200]
        for cand in best_composite:
            cand['optimization'] = 'High Sharpe & Low MinVar'
        
        # Composite candidates based on both Sharpe and Sortino ratios.
        ss_composite_candidates = [cand for cand in ss_candidates_composite if 'sharpe' in cand and 'sortino' in cand]
        
        # Check if any candidate exists before computing max values.
        if ss_composite_candidates:
            # Define weights for Sharpe and Sortino contributions.
            sharpe_weight = 0.5  # Adjust based on preference
            sortino_weight = 0.5

            # Normalize Sharpe and Sortino ratios.
            max_sharpe_value = max(cand['sharpe'] for cand in ss_composite_candidates) + 1e-15
            max_sortino_value = max(cand['sortino'] for cand in ss_composite_candidates) + 1e-15
        
            # Compute combined composite score ("MaxSS") using both weights.
            maxss_candidates = sorted(
                ss_composite_candidates,
                key=lambda x: ((x['sharpe'] / max_sharpe_value) * sharpe_weight + (x['sortino'] / max_sortino_value) * sortino_weight) / ((abs(x['risk']) + 1e-15)),
                reverse=True
            )
        
            # Select top candidates and update their optimization type.
            #best_maxss = maxss_candidates[:20]
            best_maxss = filter_candidates_by_pair_occurrence(maxss_candidates, pair_occurrence_thresholds)
            best_maxss = best_maxss[:20]
            for cand in best_maxss:
                cand['optimization'] = 'MaxSS'
        else:
            best_maxss = []

        # Apply annualization to composite candidates if requested
        if annualize:
            best_composite = annualize_candidate_list(best_composite, mt5.TIMEFRAME_M15, annualize=True)
            best_maxss = annualize_candidate_list(best_maxss, mt5.TIMEFRAME_M15, annualize=True)

        # Combine candidates in the desired order: 5 min-var, then 10 composite, then 20 max-sharpe.
        final_candidates = top_performers + best_minvar + best_composite + best_maxsharpe + best_maxsortino + best_maxomega + best_maxcalmar + best_maxmodsharpe + best_maxss
        
        # For visualization only
        visualization_candidates = final_candidates + top_return_candidates
        
        # Determine the selected candidate (if a dot was clicked) and its marker color.
        def get_marker_color(opt):
            if opt == 'High Sharpe & Low MinVar':
                return 'orange'
            elif opt == 'Min Variance':
                return 'cyan'
            elif opt == 'Max Sortino':
                return 'yellow'
            elif opt == 'MaxSS':
                return 'red'
            elif opt == 'Max Omega':
                return 'green'
            elif opt == 'Max Calmar':
                return 'dodgerblue'
            elif opt == 'Max CF Sharpe':
                return 'violet'
            else:
                return 'magenta'
        
        selected_candidate = None
        row_highlight_color = None
        
        if clickData and 'points' in clickData and clickData['points']:
            try:
                customdata_str = clickData['points'][0]['customdata']
                selected_candidate = json.loads(customdata_str)
                row_highlight_color = get_marker_color(selected_candidate.get("optimization"))
            except Exception as e:
                print(f"Error getting customdata and selected candidates (table): {e}")
                pass
        
        # Build the HTML table.
        table_header = [
            html.Tr([
                html.Th("Type"),
                html.Th("Symbols"),
                html.Th("Return"),
                html.Th("Risk"),
                html.Th("VaR 95%"),
                html.Th("CVaR 95%"),
                html.Th("DRisk"),
                html.Th("Sharpe"),
                html.Th("Sortino"),
                html.Th("Omega"),
                html.Th("Calmar"),
                html.Th("CF Sharpe"),
                html.Th("Martin"),
                html.Th("UI"),
                html.Th("Pain R."),
                html.Th("Weights")
            ])
        ]
        table_rows = []
        '''
        for port in final_candidates: # Use final_candidates which includes filtered results
            row_style = {}
            if selected_candidate and port.get('combo') == selected_candidate.get("combo"):
                row_style = {'backgroundColor': row_highlight_color, 'color': 'black'}

            # Safely get values using .get() with default 0
            row = html.Tr([
                html.Td(port.get('optimization', 'N/A')),
                html.Td(", ".join(port.get('combo', []))),
                html.Td(f"{port.get('return', 0):.4f}"),
                html.Td(f"{port.get('risk', 0):.4f}"),
                html.Td(f"{port.get('var_95', 0):.4f}"),
                html.Td(f"{port.get('cvar_95', 0):.4f}"),
                html.Td(f"{port.get('drisk', 0):.4f}"),
                html.Td(f"{port.get('sharpe', 0):.4f}"),
                html.Td(f"{port.get('sortino', 0):.4f}"),
                html.Td(f"{port.get('omega', 0):.4f}"),
                html.Td(f"{port.get('calmar', 0):.4f}"),
                html.Td(f"{port.get('mod_sharpe', 0):.4f}"),
                html.Td(f"{port.get('martin', 0):.4f}"), # Martin Ratio
                html.Td(f"{port.get('ulcer_index', 0):.4f}"), # Ulcer Index
                html.Td(f"{port.get('pain_ratio', 0):.4f}"), # Pain Ratio
                html.Td(", ".join([f"{sym}:{w:.2%}" for sym, w in zip(port.get('combo', []), port.get('weights', []))]))
            ], style=row_style)
            table_rows.append(row)
        # Assign the generated table to table_content
        '''
        table_content = html.Table(table_header + table_rows, style={'border': '1px solid white', 'width': '100%'})
        
        # Build the Efficient Frontier chart.
        frontier_fig = go.Figure()
        
        # --- Compute Efficient Frontier Line ---
        # Here we compute efficient frontier points over the entire universe (all 28 symbols)
        # using the full returns data. This is similar in spirit to PyPortfolioOpt's approach.
        # Convert log returns to arithmetic returns for portfolio optimization
        arithmetic_returns_all = convert_log_to_arithmetic_returns(returns_df)
        mean_returns_all = arithmetic_returns_all.mean()
        cov_matrix_all = arithmetic_returns_all.cov()
        n_assets_all = len(mean_returns_all)
        init_guess_all = np.repeat(1/n_assets_all, n_assets_all)
        bounds_all = tuple((-1, 1) for _ in range(n_assets_all))
        base_constraints = [{'type':'eq', 'fun': lambda w: np.sum(w)-1}]

        # Compute global minimum variance portfolio.
        res_min_all = sco.minimize(portfolio_variance, init_guess_all,
                                args=(cov_matrix_all,),
                                method='SLSQP', bounds=bounds_all, constraints=base_constraints)
        if res_min_all.success:
            w_min_all = res_min_all.x
            ret_min = np.dot(w_min_all, mean_returns_all)
            risk_min = np.sqrt(portfolio_variance(w_min_all, cov_matrix_all))
        else:
            ret_min = mean_returns_all.min()
            risk_min = None

        ret_max = mean_returns_all.max()
        
        #@memory.cache
        @timing_decorator
        def efficient_frontier_points(mean_returns, cov_matrix, num_points=50):
            frontier = []
            previous_solution = w_min_all.copy()
            targets = np.linspace(ret_min, ret_max, num_points)
            for target in targets:
                constraints_target = base_constraints + [{
                    'type': 'eq',
                    'fun': lambda w, target=target: np.dot(w, mean_returns) - target
                }]
                res = sco.minimize(
                    portfolio_variance,
                    previous_solution,
                    args=(cov_matrix,),
                    method='SLSQP',
                    bounds=bounds_all,
                    constraints=constraints_target,
                    options={'ftol': 1e-9}
                )
                if res.success:
                    risk_val = np.sqrt(portfolio_variance(res.x, cov_matrix))
                    frontier.append((risk_val, target))
                    previous_solution = res.x  # warm-start for the next iteration
            if risk_min is not None and (risk_min, ret_min) not in frontier:
                frontier.insert(0, (risk_min, ret_min))
            return frontier

        frontier_pts = efficient_frontier_points(mean_returns_all, cov_matrix_all, num_points=28)
        if frontier_pts:
            # Apply annualization to frontier data if requested
            if annualize:
                frontier_pts = annualize_frontier_data(frontier_pts, mt5.TIMEFRAME_M15, annualize=True)

            risks, rets = zip(*frontier_pts)
            frontier_fig.add_trace(go.Scatter(
                x=risks,
                y=rets,
                mode='lines',
                line=dict(color='magenta', width=2, dash='dot'),
                name='Efficient Frontier (28 pairs)'
            ))
            
        # Create candidate groups based on your optimization type:
        # Group 1: Min Variance + Composite + Max Sharpe (original frontier)
        group1 = best_maxsharpe
        # Group 2: Max Sortino candidates
        group2 = best_maxsortino
        # Group 3: Max Omega candidates
        group3 = best_maxomega
        # Group 4: Max Calmar candidates
        group4 = best_maxcalmar
        # Group 5: Combined frontier for all candidates
        group5 = best_minvar + best_composite + best_maxsharpe + best_maxsortino + best_maxomega + best_maxcalmar + best_maxmodsharpe + best_maxss
        # Group 6: Max CF Sharpe candidates
        group6 = best_maxmodsharpe

        # NOTE: Using Rust-optimized efficient_frontier_upper_hull from imported module
        # The imported function uses proper Pareto optimality instead of convex hull
        def efficient_frontier_upper_hull_local_disabled(candidates):
            points = [(c['risk'], c['return']) for c in candidates]
            if len(points) < 2:
                return points
            
            # Sort by risk ascending, then return ascending
            points = sorted(points, key=lambda x: (x[0], x[1]))
            
            def cross(o, a, b):
                """Cross product of OA x OB > 0 => counter-clockwise turn."""
                return (a[0] - o[0])*(b[1] - o[1]) - (a[1] - o[1])*(b[0] - o[0])
            
            # Build lower hull (we won't actually need it, but let's keep for completeness)
            lower = []
            for p in points:
                while len(lower) >= 2 and cross(lower[-2], lower[-1], p) <= 0:
                    lower.pop()
                lower.append(p)
            
            # Build upper hull
            upper = []
            for p in reversed(points):
                while len(upper) >= 2 and cross(upper[-2], upper[-1], p) <= 0:
                    upper.pop()
                upper.append(p)
            
            # Concatenate lower + upper hull to get the full polygon
            # but we only want the top edge.  By convention, the “upper”
            # list (minus its last point) goes left->right across the top.
            # In many “efficient frontier” plots, you only need `upper`.
            # Typically, the last point in `upper` is the same as the first
            # in `lower`, so we slice them out.
            # If you literally only want the top boundary (no lower hull),
            # just return the “upper” chain in left-to-right order:
            frontier = list(reversed(upper))[1:]  # skip the last to avoid duplication
            return frontier

        #@memory.cache
        @timing_decorator
        def compute_all_hulls_parallel(groups):
            """Compute convex hulls for multiple portfolio groups in parallel with CPU limiting"""

            # Calculate optimal cores based on current CPU usage
            #num_cores = min(6, get_optimal_cores(max_usage_percent=80))

            # Use a new parallel executor to avoid conflicts
            parallel_executor = create_parallel_executor()
            delayed_tasks = [delayed(efficient_frontier_upper_hull)(group) for group in groups]
            results = safe_parallel_execute(parallel_executor, delayed_tasks)
            return results

        # Use the parallelized function
        groups = [group1, group2, group3, group4, group5, group6]
        group_names = ['MaxSharpe', 'MaxSortino', 'MaxOmega', 'MaxCalmar', 'Full', 'MaxCFSharpe']
        group_colors = ['magenta', 'yellow', 'green', 'dodgerblue', 'white', 'violet']
        group_widths = [2, 2, 2, 2, 4, 2]

        all_hulls = compute_all_hulls_parallel(groups)

        # Add traces for all groups
        for hull, name, color, width in zip(all_hulls, group_names, group_colors, group_widths):
            if hull:
                rx, ry = zip(*hull)
                frontier_fig.add_trace(
                    go.Scatter(
                        x=rx, y=ry, mode='lines',
                        line=dict(color=color, width=width),
                        name=f'Frontier ({name})'
                    )
                )

        # --- Build candidate dots on the Efficient Frontier Chart ---

        # Add this function after preparing all candidates but before adding traces to frontier_fig
        def get_optimization_priority(opt_type):
            """Return priority value for optimization types (lower value = higher priority = appears on top)"""
            priority_map = {
                "Max Sharpe": 1,
                "Top Sharpe": 1,
                "Max Calmar": 2, 
                "Top Calmar": 2,
                "Max Sortino": 3,
                "Top Sortino": 3,
                "Max Omega": 4,
                "Top Omega": 4,
                "MaxSS": 5,
                "High Sharpe & Low MinVar": 6,
                "Min Variance": 7,
                "Max Return": 8,  # Can adjust this priority as needed
                "Max CF Sharpe": 9, 
                "Top CF Sharpe": 9
            }
            return priority_map.get(opt_type, 9)  # Default priority for unlisted types
        
        all_visualization_candidates = sorted(
            visualization_candidates,
            key=lambda x: get_optimization_priority(x.get('optimization', ''))
        )

        # Optimized chart generation - group candidates by optimization type for vectorized plotting
        def add_optimized_portfolio_traces(fig, candidates, selected_candidate, x_field='risk', x_label='Risk'):
            """Add portfolio candidates as grouped traces for better performance"""
            # Group candidates by optimization type and styling
            grouped_candidates = {}

            for port in candidates:
                is_top_performer = port['optimization'].startswith('Top ')
                is_top_return = port['optimization'] == 'Max Return'
                is_selected = selected_candidate and port['combo'] == selected_candidate.get("combo")

                # Determine styling
                if is_top_return:
                    marker_symbol = 'x'
                    marker_size = 12 if is_selected else 10
                elif is_top_performer:
                    marker_symbol = 'asterisk'
                    marker_size = 12 if is_selected else 10
                else:
                    marker_symbol = 'circle'
                    marker_size = 10

                # Determine color
                if is_selected:
                    marker_color = "white"
                    marker_size = 14
                elif port['optimization'] == 'Max Return':
                    marker_color = 'blue'
                elif port['optimization'] == 'High Sharpe & Low MinVar':
                    marker_color = 'orange'
                elif port['optimization'] == 'Min Variance':
                    marker_color = 'cyan'
                elif port['optimization'] == 'MaxSS':
                    marker_color = 'red'
                elif port['optimization'] == 'Max CF Sharpe':
                    marker_color = 'violet'
                else:
                    marker_color = (
                        'magenta' if port['optimization'] in ['Max Sharpe', 'Top Sharpe']
                        else 'yellow' if port['optimization'] in ['Max Sortino', 'Top Sortino']
                        else 'green' if port['optimization'] in ['Max Omega', 'Top Omega']
                        else 'dodgerblue' if port['optimization'] in ['Max Calmar', 'Top Calmar']
                        else 'gray'
                    )

                # Create grouping key based on styling
                group_key = (port['optimization'], marker_color, marker_symbol, marker_size, is_selected)

                if group_key not in grouped_candidates:
                    grouped_candidates[group_key] = {
                        'x': [], 'y': [], 'customdata': [], 'meta': [], 'text': []
                    }

                # Add data to group
                total = sum(abs(w) for w in port['weights'])
                normalized_weights = ", ".join(f"{w/total:.2f}" for w in port['weights'])
                tooltip = json.dumps({
                    "combo": port['combo'],
                    "weights": port['weights'],
                    "optimization": port['optimization'],
                    "risk": port.get('risk', 0),
                    "return": port.get('return', 0),
                    "cvar_95": port.get('cvar_95', 0),
                    "sharpe": port.get('sharpe', 0),
                    "sortino": port.get('sortino', 0),
                    "omega": port.get('omega', 0),
                    "calmar": port.get('calmar', 0),
                    "mod_sharpe": port.get('mod_sharpe', 0)
                })

                grouped_candidates[group_key]['x'].append(port.get(x_field, 0))
                grouped_candidates[group_key]['y'].append(port.get('return', 0))
                grouped_candidates[group_key]['customdata'].append(tooltip)
                grouped_candidates[group_key]['meta'].append(normalized_weights)
                grouped_candidates[group_key]['text'].append(", ".join(port['combo']))

            # Create traces for each group
            for (opt_type, color, symbol, size, is_selected), data in grouped_candidates.items():
                if not data['x']:  # Skip empty groups
                    continue

                # Build hover template
                hover_template = (
                    f"{x_label}: %{{x:.4f}}<br>" +
                    "Return: %{y:.4f}<br>" +
                    "Symbols: %{text}<br>" +
                    "Weights: %{meta}<br>" +
                    "Optimization: " + opt_type + "<br>" +
                    "<extra></extra>"  # Simplified hover for performance
                )

                trace_name = f"{opt_type}" + (" (Selected)" if is_selected else "")

                fig.add_trace(go.Scatter(
                    x=data['x'],
                    y=data['y'],
                    mode='markers',
                    name=trace_name,
                    marker=dict(
                        size=size,
                        color=color,
                        symbol=symbol,
                        line=dict(width=2, color='white') if symbol in ['asterisk', 'x'] else None
                    ),
                    customdata=data['customdata'],
                    meta=data['meta'],
                    text=data['text'],
                    hovertemplate=hover_template,
                    showlegend=False  # Reduce legend clutter
                ))

        # Add optimized portfolio traces to frontier chart
        add_optimized_portfolio_traces(frontier_fig, all_visualization_candidates, selected_candidate)

        # --- Add main pair dots as small gray stars
        # Use the main symbols list (28 pairs) to mark their individual risk and return.
        main_pairs = symbols  # 'symbols' was defined earlier as the full list of main pairs.
        for sym in main_pairs:
            if sym in returns_df.columns:
                # For a single symbol, we can use its standard deviation as risk
                # and its mean return as expected return.
                # Convert log returns to arithmetic returns for portfolio metrics
                arithmetic_sym_returns = convert_log_to_arithmetic_returns(returns_df[sym])
                risk_val = arithmetic_sym_returns.std()
                raw_return = arithmetic_sym_returns.mean()
                return_val = abs(arithmetic_sym_returns.mean())
                
                # Format the display symbol with negative prefix if needed
                display_sym = f"-{sym}" if raw_return < 0 else sym

                frontier_fig.add_trace(go.Scatter(
                    x=[risk_val],
                    y=[return_val],
                    mode='markers',
                    marker=dict(symbol='star', size=8, color='gray'),
                    name=f'{sym} (Main Pair)',
                    hovertemplate=f"{display_sym}: Risk: %{{x:.4f}}<br>Return: %{{y:.4f}}<extra></extra>"
                ))

        # Compute max risk value from max Sharpe candidates.
        if final_candidates:
            max_sharpe_risk = max(port['risk'] for port in final_candidates)
        else:
            max_sharpe_risk = 0.001
        # Add this code to calculate max_cvar early:
        if final_candidates:
            max_cvar = max(port.get('cvar_95', 0.001) for port in final_candidates)
        else:
            max_cvar = 0.001

        # Find recommended portfolios for standard frontier
        best_returns, best_metric, best_hedge, mid_frontier = find_recommended_portfolios(
            visualization_candidates, returns_df, 
            all_hulls[4],  # Using the "Full" hull (index 4)
            type='risk'
        )

        recommended_portfolios = [best_returns, best_metric, best_hedge, mid_frontier]
        
        # Create dynamic titles based on annualization status
        timeframe_desc = get_timeframe_description(mt5.TIMEFRAME_M15, annualize)
        frontier_title = f"Efficient Frontier - {timeframe_desc}"
        risk_title = f"Risk (Std. Dev.) - {timeframe_desc}"
        return_title = f"Return - {timeframe_desc}"

        frontier_fig.update_layout(
            title=frontier_title,
            xaxis_title=risk_title,
            #xaxis=dict(range=[0, max_sharpe_risk + 10]),
            xaxis=dict(
                range=[0, max_sharpe_risk + 0.0001],
                #autorange=True,  # Enable autorange to adapt to your data
                showgrid=True,   # Show grid lines
                gridcolor='rgba(100, 100, 100, 0.2)'  # Subtle grid
            ),
            yaxis_title=return_title,
            #width=2000,  # adjust width (2x default, for example)
            height=1000,  # adjust height (2x default, for example)
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white'),
            updatemenus=[
                dict(
                    type="buttons",
                    direction="right",
                    buttons=[
                        dict(
                            args=[{"xaxis.autorange": True, "yaxis.autorange": True}],
                            label="Auto Range",
                            method="relayout"
                        ),
                        dict(
                            args=[{"xaxis.autorange": False, "xaxis.range": [0, max_sharpe_risk], 
                                "yaxis.autorange": True}],
                            label="Focus Risk",
                            method="relayout"
                        )
                    ],
                    pad={"r": 10, "t": 10},
                    showactive=False,
                    x=0.15,
                    y=1.15,
                    xanchor="left",
                    yanchor="top",
                    bgcolor="rgba(60, 60, 60, 0.7)",
                    bordercolor="rgba(200, 200, 200, 0.5)"
                )
            ]
        )

        # After computing max_sharpe_risk and updating frontier_fig layout:
        frontier_fig.add_shape(
            type="line",
            xref="x",
            yref="paper",
            x0=0.90 * max_sharpe_risk,
            x1=0.90 * max_sharpe_risk,
            y0=0,
            y1=1,
            line=dict(color="red", dash="dot", width=2),
            name="90%"
        )
        frontier_fig.add_shape(
            type="line",
            xref="x",
            yref="paper",
            x0=0.80 * max_sharpe_risk,
            x1=0.80 * max_sharpe_risk,
            y0=0,
            y1=1,
            line=dict(color="aqua", dash="dot", width=2),
            name="80%"
        )
        frontier_fig.add_shape(
            type="line",
            xref="x",
            yref="paper",
            x0=0.60 * max_sharpe_risk,
            x1=0.60 * max_sharpe_risk,
            y0=0,
            y1=1,
            line=dict(color="lime", dash="dot", width=2),
            name="60%"
        )
        frontier_fig.add_shape(
            type="line",
            xref="x",
            yref="paper",
            x0=0.40 * max_sharpe_risk,
            x1=0.40 * max_sharpe_risk,
            y0=0,
            y1=1,
            line=dict(color="yellow", dash="dot", width=2),
            name="40%"
        )

        # Build the portfolio return and quadrant charts if a dot is clicked.
        portfolio_returns_fig = go.Figure()
        if clickData and 'points' in clickData and clickData['points']:
            try:
                # First check if customdata exists and is a valid format
                customdata_raw = clickData['points'][0].get('customdata')
                
                if customdata_raw is None:
                    raise ValueError("No customdata available in the clicked point")
                    
                # Handle both string and object formats of customdata
                if not isinstance(customdata_raw, (str, bytes, bytearray)):
                    customdata_str = json.dumps(customdata_raw)
                else:
                    customdata_str = customdata_raw
                
                # Try parsing the JSON string
                port_info = json.loads(customdata_str)
                
                # Check if this is a combined portfolio and handle accordingly
                if port_info.get('is_combined_portfolio') and 'source_portfolios' in port_info:
                    print("Processing combined portfolio from button")
                    source_portfolios = port_info['source_portfolios']
                    
                    # Filter out any None portfolios
                    source_portfolios = [p for p in source_portfolios if p is not None]
                    
                    if source_portfolios:
                        # Apply equal weighting to all valid portfolios
                        weights = [0.25] * len(source_portfolios)  # Default to 25% each
                        if len(source_portfolios) != 4:  # If we don't have exactly 4 portfolios
                            weights = [1.0/len(source_portfolios)] * len(source_portfolios)  # Equal weights
                        
                        # Combine the portfolios
                        combined_portfolio = combine_portfolios(source_portfolios, weights)
                        combined_portfolio['optimization'] = "Combined Portfolio"
                        
                        # Replace port_info with our combined portfolio
                        port_info = combined_portfolio
                        print(f"Successfully combined {len(source_portfolios)} portfolios")
                
                # Verify required fields exist
                if "combo" not in port_info or "weights" not in port_info:
                    raise ValueError("Missing required fields in customdata")
                
                selected_combo = port_info["combo"]
                selected_weights = port_info["weights"]
            
            except Exception as e:
                print(f"Error processing clickData: {e}")
                # Create default figures with error message
                portfolio_returns_fig = go.Figure()
                portfolio_returns_fig.update_layout(
                    title=f"Error processing portfolio: {str(e)}",
                    paper_bgcolor='black',
                    plot_bgcolor='black',
                    font=dict(color='white')
                )
                # Return default/empty values for ALL outputs on error
                return (html.Div(f"Error processing click data: {e}"), empty_fig, portfolio_returns_fig, False, "", empty_fig, None, None, None)#, # MPT/CVaR outputs - empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_fig) # Market Phase outputs
            
            # Convert each displayed symbol (with optional '-' prefix) to base symbol with positive/negative weight
            base_symbols = []
            adjusted_weights = []
            
            for sym, weight in zip(selected_combo, selected_weights):
                base_symbols.append(sym)
                adjusted_weights.append(weight)

            # Build the candidate portfolio series.
            # During weekends, use full_returns_df for portfolio chart to show complete Friday data
            portfolio_data_source = full_returns_df if should_use_friday_data() else returns_df
            subset_returns = portfolio_data_source[base_symbols]
            port_return_series = pd.Series(0, index=subset_returns.index)
            for base, weight in zip(base_symbols, adjusted_weights):
                port_return_series += subset_returns[base] * weight

            # Compute cumulative returns.
            cum_returns = port_return_series.cumsum()
            # Filter out weekend dates.
            cum_returns = cum_returns[cum_returns.index.dayofweek < 5]

            # Convert index to timezone-naive local time for correct chart display
            cum_returns.index = cum_returns.index.tz_convert('Europe/Bucharest').tz_localize(None)
            
            # Plot the portfolio cumulative returns chart.
            portfolio_returns_fig.add_trace(go.Scatter(
                x=cum_returns.index,
                y=cum_returns,
                mode='lines',
                line=dict(color='white', width=3),
                name="Cumulative Returns"
            ))
            portfolio_returns_fig.update_layout(
                title="Portfolio Cumulative Returns",
                xaxis_title="Time",
                yaxis_title="Cumulative Return",
                paper_bgcolor='black',
                plot_bgcolor='black',
                #width=2000,  # adjust width (2x default, for example)
                height=1000,  # adjust height (2x default, for example)
                font=dict(color='white')
            )
        
            # Assuming portfolio_cum_returns holds the cumulative returns series
            current_value = cum_returns.iloc[-1]
            portfolio_returns_fig.add_shape(
                type="line",
                x0=cum_returns.index[0],
                y0=current_value,
                x1=cum_returns.index[-1],
                y1=current_value,
                line=dict(color="red", dash="dash", width=2),
                xref="x",
                yref="y"
            )
            # Add an annotation showing the last y-axis value at the right-end of the line.
            portfolio_returns_fig.add_annotation(
                x=cum_returns.index[-1],
                y=current_value,
                xref="x",
                yref="y",
                text=f"{current_value:.4f}",
                xanchor="left",
                yanchor="bottom",
                showarrow=False,
                font=dict(color="red", size=12),
                bgcolor="black"
            )

            # After you process the clicked portfolio
            try:
                # Define scale_total here to ensure it's available
                scale_total = annotation_total_weight if annotation_total_weight is not None else 0.20
                
                # Determine which frontier was clicked based on stored data or trigger_id
                frontier_type = None
                if clicked_portfolio_data_to_store and 'frontier_type' in clicked_portfolio_data_to_store:
                    frontier_type = clicked_portfolio_data_to_store['frontier_type']
                elif trigger_id == 'efficient-frontier':
                     frontier_type = 'efficient-frontier'
                elif trigger_id == 'cvar-frontier':
                     frontier_type = 'cvar-frontier'

                # Generate suggestions based on the determined frontier type
                # Use port_info directly, as it contains the data from the actual clicked point's customdata
                if frontier_type == 'efficient-frontier':
                    try:
                        # Ensure port_info (parsed from customdata earlier) is used as the clicked_portfolio
                        clicked_portfolio_from_click = port_info # Use the parsed data

                        # Ensure clicked_portfolio has all required fields (using the data from the click)
                        clicked_portfolio_from_click['risk'] = clicked_portfolio_from_click.get('risk', 0.0001)
                        clicked_portfolio_from_click['return'] = clicked_portfolio_from_click.get('return', 0)
                        clicked_portfolio_from_click['cvar_95'] = clicked_portfolio_from_click.get('cvar_95', 0)

                        print(f"--- Debug EF: Using clicked_portfolio from clickData: {clicked_portfolio_from_click['combo']}") # Add debug print

                        # Generate portfolio suggestions using the correct clicked portfolio data
                        suggested_portfolios = generate_portfolio_suggestions(
                            clicked_portfolio_from_click, visualization_candidates, max_sharpe_risk
                        )

                        # Combine clicked portfolio with suggested portfolios
                        all_portfolios = [clicked_portfolio_from_click] + suggested_portfolios # Use correct variable
                        # Equal weights for all portfolios
                        weights = [1.0 / len(all_portfolios)] * len(all_portfolios)

                        # Combine using the function from func_portfolio (or gfx_funcs if it's there)
                        combined_portfolio = combine_portfolios(all_portfolios, weights)

                        # MPT string will be created after suggestion generation succeeds
                    except Exception as e:
                        print("Error processing Efficient Frontier click:", e)
                        # raise dash.exceptions.PreventUpdate # Comment out or handle differently if needed
                # Use port_info directly, as it contains the data from the actual clicked point's customdata
                elif frontier_type == 'cvar-frontier':
                    try: # Outer try for the whole CVaR block
                        # Ensure port_info (parsed from customdata earlier) is used as the clicked_portfolio
                        clicked_portfolio_from_click = port_info # Use the parsed data
                        # Mark this as using CVaR for risk measure in suggestion generation
                        clicked_portfolio_from_click['use_cvar'] = True

                        # Ensure clicked_portfolio has all required fields (using the data from the click)
                        clicked_portfolio_from_click['risk'] = clicked_portfolio_from_click.get('risk', 0.0001)
                        clicked_portfolio_from_click['return'] = clicked_portfolio_from_click.get('return', 0)
                        clicked_portfolio_from_click['cvar_95'] = clicked_portfolio_from_click.get('cvar_95', 0)

                        print(f"--- Debug CVaR: Using clicked_portfolio from clickData: {clicked_portfolio_from_click['combo']}") # Add debug print

                        # Generate portfolio suggestions using the correct clicked portfolio data
                        suggested_portfolios = generate_portfolio_suggestions(
                            clicked_portfolio_from_click, visualization_candidates, max_cvar
                        )

                        # Combine clicked portfolio with suggested portfolios
                        all_portfolios = [clicked_portfolio_from_click] + suggested_portfolios # Use correct variable
                        # Equal weights for all portfolios
                        weights = [1.0 / len(all_portfolios)] * len(all_portfolios)

                        # Combine using the function from func_portfolio (or gfx_funcs if it's there)
                        combined_portfolio = combine_portfolios(all_portfolios, weights)

                        # MPT string will be created after suggestion generation succeeds
                    except Exception as e: # This except corresponds to the inner CVaR try
                         print(f"Error processing CVaR Frontier click: {e}")
                         print(f"Error processing CVaR Frontier click: {e}")
                         # Keep suggested_portfolios as potentially empty list

                # --- Generate MPT String (only if suggestions were generated successfully) ---
                # This block is now outside the EF/CVaR specific try blocks, but inside the main suggestion try block
                try:
                    # Ensure port_info and suggested_portfolios are valid before creating the string
                    if port_info and 'combo' in port_info and 'weights' in port_info: # Check port_info validity
                         combined_mpt_string = create_combined_mpt_string(port_info, suggested_portfolios)
                    else:
                         print("Skipping MPT string creation due to invalid port_info.")
                         combined_mpt_string = "" # Ensure it's empty if port_info is bad
                except Exception as mpt_e:
                    print(f"Error creating MPT string: {mpt_e}")
                    combined_mpt_string = "" # Reset on MPT string creation error

                # --- Add Annotations ---
                # Add annotation for suggestions to portfolio_returns_fig
                if suggested_portfolios:
                    suggestion_text = "Suggested Additional Portfolios:<br><br>"

                    for i, suggestion in enumerate(suggested_portfolios):
                        # Format normalized weights
                        total_weight = sum(abs(w) for w in suggestion['weights'])
                        # Ensure scale_total is defined (it's defined earlier around line 1383)
                        normalized_weights = [(w/total_weight)*scale_total for w in suggestion['weights']]
                        symbols_with_weight = ", ".join(f"{sym}: {w:.2f}" for sym, w in zip(suggestion['combo'], normalized_weights))

                        suggestion_text += (
                            f"Suggestion {i+1} ({suggestion['risk_description']}):<br>"
                            f"{'Risk (CVaR 95%)' if suggestion.get('used_risk_measure') == 'cvar_95' else 'Risk (Std.Dev.)'}: {suggestion.get(suggestion.get('used_risk_measure', 'risk'), 0):.4f} / Return: {suggestion.get('return', 0):.4f}<br>"
                            f"Sharpe: {suggestion.get('sharpe', 0):.4f} / Sortino: {suggestion.get('sortino', 0):.4f} / Omega: {suggestion.get('omega', 0):.4f} / Calmar: {suggestion.get('calmar', 0):.4f} / CF Sharpe: {suggestion.get('mod_sharpe', 0):.4f}<br>"
                            f"Symbols: {symbols_with_weight}<br>"
                            f"MPT Input: {suggestion['mpt_string']}<br><br>"
                        )

                    # Add the suggestions annotation
                    portfolio_returns_fig.add_annotation(
                        x=0.01,
                        y=0.15,  # Position at bottom left
                        xref="paper",
                        yref="paper",
                        text=suggestion_text,
                        align="left",
                        showarrow=False,
                        font=dict(color="white", size=14),
                        bordercolor="white",
                        borderwidth=1,
                        bgcolor="rgba(0,0,0,0.8)"
                    )
            except Exception as e: # This except now catches errors from suggestion generation OR MPT string creation
                print(f"Error during suggestion or MPT string generation: {e}")
                # Optional: Add an annotation about the error
                portfolio_returns_fig.add_annotation(
                    x=0.01,
                    y=0.15,
                    xref="paper",
                    yref="paper",
                    text=f"Error generating suggestions/MPT string: {str(e)}",
                    align="left",
                    showarrow=False,
                    font=dict(color="red", size=14),
                    bordercolor="white",
                    borderwidth=1,
                    bgcolor="rgba(0,0,0,0.8)"
                )
                # Ensure combined_mpt_string is empty on error
                combined_mpt_string = ""

            # Remove weekend gaps on the x-axis.
            portfolio_returns_fig.update_xaxes(rangebreaks=[dict(bounds=["sat", "mon"])])
                        
            # After computing candidate portfolio series, add annotation to portfolio_returns_fig.
            # --- Get risk/return values reliably from port_info ---
            if port_info.get('frontier_type') == 'cvar-frontier':
                # Use CVaR if CVaR frontier was the source
                risk_val = port_info.get('cvar_95', 0)
                risk_key_for_display = 'CVaR 95%'
            else:
                # Default to standard risk (Std.Dev.)
                risk_val = port_info.get('risk', 0)
                risk_key_for_display = 'Risk (Std.Dev.)'
            return_val = port_info.get('return', 0)
            # --- End reliable value retrieval ---

            # Get optimization, sharpe and sortino from the candidate info.
            optimization = port_info.get("optimization", "N/A")
            sharpe = port_info.get("sharpe", 0)
            sortino = port_info.get("sortino", 0)
            omega = port_info.get("omega", 0)
            calmar = port_info.get("calmar", 0)
            cfsharpe = port_info.get("mod_sharpe", 0)
            # Compute normalized weights scaled so that their total is annotation_total_weight
            total_candidate_weight = sum(abs(w) for w in selected_weights)
            scale_total = annotation_total_weight if annotation_total_weight is not None else 0.20
            normalized_weights = [(w/total_candidate_weight)*scale_total for w in selected_weights]
            symbols_with_weight = ", ".join(f"{sym}: {w:.2f}" for sym, w in zip(selected_combo, normalized_weights))

            annotation_text = (
                f"{risk_key_for_display}: {risk_val:.4f}<br>" # Use dynamic risk key label
                f"Return: {return_val:.4f}<br>"
                f"Optimization: {optimization}<br>"
                f"Sharpe: {sharpe:.4f} / Sortino: {sortino:.4f} / Omega: {omega:.4f} / Calmar: {calmar:.4f} / CF Sharpe: {cfsharpe:.4f}<br>"
                f"Symbols: {symbols_with_weight}"
            )
            print(f"DEBUG: Annotation text for main portfolio: {annotation_text}") # Add print statement
            portfolio_returns_fig.add_annotation(
                x=0.01,
                y=0.99,
                xref="paper",
                yref="paper",
                text=annotation_text,
                showarrow=False,
                font=dict(color="white", size=14),
                bordercolor="white",
                borderwidth=1,
                bgcolor="black"
            )
        else:
            portfolio_returns_fig.update_layout(
                title="Portfolio Cumulative Returns (Click a dot in the Efficient Frontier)",
                paper_bgcolor='black',
                plot_bgcolor='black',
                font=dict(color='white')
            )
            portfolio_returns_fig.update_xaxes(rangebreaks=[dict(bounds=["sat", "mon"])])

        # Create a new figure for CVaR frontier
        cvar_frontier_fig = go.Figure()
        
        # Create candidate groups for CVaR frontier using the same categorization logic but sorted by CVaR
        cvar_group1 = sorted(best_maxsharpe, key=lambda x: x.get('cvar_95', 0))
        cvar_group2 = sorted(best_maxsortino, key=lambda x: x.get('cvar_95', 0))
        cvar_group3 = sorted(best_maxomega, key=lambda x: x.get('cvar_95', 0))
        cvar_group4 = sorted(best_maxcalmar, key=lambda x: x.get('cvar_95', 0))
        cvar_group5 = sorted(best_minvar + best_composite + best_maxsharpe + best_maxsortino +
                        best_maxomega + best_maxcalmar + best_maxmodsharpe + best_maxss, key=lambda x: x.get('cvar_95', 0))
        cvar_group6 = sorted(best_maxmodsharpe, key=lambda x: x.get('cvar_95', 0))

        # Create a CVaR-specific efficient frontier hull function
        # NOTE: Using Rust-optimized efficient_frontier_upper_hull_cvar from imported module
        # The imported function uses proper Pareto optimality instead of convex hull
        def efficient_frontier_upper_hull_cvar_local_disabled(candidates):
            # Similar to the original, but using CVaR instead of risk
            # Similar to the original, but using CVaR instead of risk
            points = [(c.get('cvar_95', 0), c['return']) for c in candidates]
            if len(points) < 2:
                return points
            
            # Sort by CVaR ascending, then return ascending
            points = sorted(points, key=lambda x: (x[0], x[1]))
            
            def cross(o, a, b):
                """Cross product of OA x OB > 0 => counter-clockwise turn."""
                return (a[0] - o[0])*(b[1] - o[1]) - (a[1] - o[1])*(b[0] - o[0])
            
            # Build upper hull
            upper = []
            for p in reversed(points):
                while len(upper) >= 2 and cross(upper[-2], upper[-1], p) <= 0:
                    upper.pop()
                upper.append(p)
            
            frontier = list(reversed(upper))[1:]  # skip the last to avoid duplication
            return frontier
        
        # Compute CVaR hulls in parallel
        cvar_groups = [cvar_group1, cvar_group2, cvar_group3, cvar_group4, cvar_group5, cvar_group6]

        # Use a new parallel executor for CVaR hulls to avoid conflicts
        parallel_executor = create_parallel_executor()
        cvar_hulls = parallel_executor(
            delayed(efficient_frontier_upper_hull_cvar)(group) for group in cvar_groups
        )
        
        # Add traces for all CVaR groups
        for hull, name, color, width in zip(cvar_hulls, group_names, group_colors, group_widths):
            if hull:
                rx, ry = zip(*hull)
                cvar_frontier_fig.add_trace(
                    go.Scatter(
                        x=rx, y=ry, mode='lines',
                        line=dict(color=color, width=width),
                        name=f'Frontier ({name})'
                    )
                )
        
        # Add optimized portfolio traces to CVaR chart
        add_optimized_portfolio_traces(cvar_frontier_fig, all_visualization_candidates, selected_candidate, 'cvar_95', 'CVaR 95%')

        # Add reference lines at CVaR levels
        if max_cvar > 0:
            cvar_frontier_fig.add_shape(
                type="line",
                xref="x",
                yref="paper",
                x0=0.90 * max_cvar,
                x1=0.90 * max_cvar,
                y0=0,
                y1=1,
                line=dict(color="red", dash="dot", width=2),
                name="90%"
            )
            cvar_frontier_fig.add_shape(
                type="line",
                xref="x",
                yref="paper",
                x0=0.80 * max_cvar,
                x1=0.80 * max_cvar,
                y0=0,
                y1=1,
                line=dict(color="aqua", dash="dot", width=2),
                name="80%"
            )
            cvar_frontier_fig.add_shape(
                type="line",
                xref="x",
                yref="paper",
                x0=0.60 * max_cvar,
                x1=0.60 * max_cvar,
                y0=0,
                y1=1,
                line=dict(color="lime", dash="dot", width=2),
                name="60%"
            )
            cvar_frontier_fig.add_shape(
                type="line",
                xref="x",
                yref="paper",
                x0=0.40 * max_cvar,
                x1=0.40 * max_cvar,
                y0=0,
                y1=1,
                line=dict(color="yellow", dash="dot", width=2),
                name="40%"
            )

        # Similarly for CVaR frontier:
        best_returns_cvar, best_metric_cvar, best_hedge_cvar, mid_frontier_cvar = find_recommended_portfolios(
            visualization_candidates, returns_df,
            cvar_hulls[4],  # Using the "Full" hull for CVaR
            type='cvar'
        )

        # Find recommended portfolios for CVaR frontier
        recommended_cvar_portfolios = [best_returns_cvar, best_metric_cvar, best_hedge_cvar, mid_frontier_cvar]

        # Update layout for the CVaR frontier chart
        cvar_frontier_fig.update_layout(
            title=f"Efficient Frontier (CVaR vs Return) - {timeframe_desc}", # Revert title
            xaxis_title=f"CVaR 95% - {timeframe_desc}", # Revert x-axis title
            xaxis=dict(
                range=[0, max_cvar + 0.0001], # Revert range calculation
                showgrid=True,
                gridcolor='rgba(100, 100, 100, 0.2)'
            ),
            yaxis_title=return_title,
            height=1000,
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white'),
            updatemenus=[
                dict(
                    type="buttons",
                    direction="right",
                    buttons=[
                        dict(
                            args=[{"xaxis.autorange": True, "yaxis.autorange": True}],
                            label="Auto Range",
                            method="relayout"
                        ),
                        dict(
                            args=[{"xaxis.autorange": False, "xaxis.range": [0, max_cvar], # Revert range
                                "yaxis.autorange": True}],
                            label="Focus CVaR", # Revert button label
                            method="relayout"
                        )
                    ],
                    pad={"r": 10, "t": 10},
                    showactive=False,
                    x=0.15,
                    y=1.15,
                    xanchor="left",
                    yanchor="top",
                    bgcolor="rgba(60, 60, 60, 0.7)",
                    bordercolor="rgba(200, 200, 200, 0.5)"
                )
            ]
        )

        # Highlight the recommended portfolios on the charts with distinct markers
        if any(recommended_portfolios):
            for i, portfolio in enumerate(recommended_portfolios):
                if not portfolio:
                    continue
                    
                # Use different symbols for each recommendation - ADD FOURTH ELEMENT
                symbols = ['star', 'diamond', 'cross', 'triangle-up']
                colors = ['gold', 'cyan', 'lime', 'magenta']
                sizes = [16, 16, 16, 16]
                
                # Add special markers to standard frontier
                frontier_fig.add_trace(go.Scatter(
                    x=[portfolio['risk']],
                    y=[portfolio['return']],
                    mode='markers',
                    marker=dict(
                        size=sizes[i],
                        color=colors[i],
                        symbol=symbols[i],
                        line=dict(width=2, color='red')
                    ),
                    name=f"Recommendation {i+1}",
                    hoverinfo='skip'
                ))
                
                # Add correlation info to hover if available
                if 'correlation_to_best' in portfolio and i > 0:
                    frontier_fig.add_annotation(
                        x=portfolio['risk'],
                        y=portfolio['return'],
                        text=f"Corr: {portfolio['correlation_to_best']:.2f}",
                        font=dict(family="sans serif", size=18, color="red"),
                        showarrow=True,
                        arrowhead=2,
                        arrowcolor=colors[i],
                        ax=-40,
                        ay=-40
                    )

        # Do the same for CVaR frontier
        if any(recommended_cvar_portfolios):
            for i, portfolio in enumerate(recommended_cvar_portfolios):
                if not portfolio:
                    continue

                # Use different symbols for each recommendation - ADD FOURTH ELEMENT
                symbols = ['star', 'diamond', 'cross', 'triangle-up']
                colors = ['gold', 'cyan', 'lime', 'magenta']
                sizes = [16, 16, 16, 16]

                # Add special markers to CVaR frontier
                cvar_frontier_fig.add_trace(go.Scatter(
                    x=[portfolio.get('cvar_95', 0)],
                    y=[portfolio['return']],
                    mode='markers',
                    marker=dict(
                        size=sizes[i],
                        color=colors[i],
                        symbol=symbols[i],
                        line=dict(width=2, color='red')
                    ),
                    name=f"Recommendation {i+1}",
                    hoverinfo='skip'
                ))

                # Add correlation info to hover if available
                if 'correlation_to_best' in portfolio and i > 0:
                    cvar_frontier_fig.add_annotation(
                        x=portfolio.get('cvar_95', 0),
                        y=portfolio['return'],
                        text=f"Corr: {portfolio['correlation_to_best']:.2f}",
                        font=dict(family="sans serif", size=18, color="red"),
                        showarrow=True,
                        arrowhead=2,
                        arrowcolor=colors[i],
                        ax=-40,
                        ay=-40
                    )

        def normalize_portfolio(portfolio):
            if not portfolio:
                return None

            # Create a copy to avoid modifying the original
            normalized = portfolio.copy()

            # If weights exist, normalize them so absolute sum = 1.0
            if 'weights' in normalized and normalized['weights']:
                abs_sum = sum(abs(w) for w in normalized['weights'])
                if abs_sum > 0:
                    normalized['weights'] = [w/abs_sum for w in normalized['weights']]

            return normalized

        # --- Final Return ---
        # (This block is removed as normalize_portfolio is now defined above)

        # Apply normalization to both sets of portfolios
        recommended_portfolios = [normalize_portfolio(p) for p in recommended_portfolios]
        recommended_cvar_portfolios = [normalize_portfolio(p) for p in recommended_cvar_portfolios]

        # Store both combinations in JSON format
        risk_portfolios_json = json.dumps([p if p is None else {
            "combo": p.get("combo", []),
            "weights": p.get("weights", []),
            "optimization": p.get("optimization", ""),
            "return": float(p.get("return", 0)),
            "risk": float(p.get("risk", 0)),
            "sharpe": float(p.get("sharpe", 0)),
            "sortino": float(p.get("sortino", 0)),
            "omega": float(p.get("omega", 0)),
            "calmar": float(p.get("calmar", 0)),
            "cvar_95": float(p.get("cvar_95", 0))
        } for p in recommended_portfolios])

        cvar_portfolios_json = json.dumps([p if p is None else {
            "combo": p.get("combo", []),
            "weights": p.get("weights", []),
            "optimization": p.get("optimization", ""),
            "return": float(p.get("return", 0)),
            "risk": float(p.get("risk", 0)),
            "sharpe": float(p.get("sharpe", 0)), 
            "sortino": float(p.get("sortino", 0)),
            "omega": float(p.get("omega", 0)),
            "calmar": float(p.get("calmar", 0)),
            "cvar_95": float(p.get("cvar_95", 0))
        } for p in recommended_cvar_portfolios])

        # --- Market Phase Analysis is now handled by a separate callback ---

        # --- Final Return ---
        # Return tuple length must match the number of outputs (9)
        result_tuple = (table_content, frontier_fig, portfolio_returns_fig, False, combined_mpt_string, cvar_frontier_fig, # MPT/CVaR outputs
                       risk_portfolios_json, cvar_portfolios_json, clicked_portfolio_data_to_store) # Store outputs

        # Cache the result for weekend use (only during trading days)
        cache_friday_data(cache_key, result_tuple)

        # Perform memory cleanup if memory manager is available
        if memory_manager:
            try:
                stats = memory_manager.get_current_stats()
                if stats.usage_percent > 70:  # Cleanup if usage is high
                    print(f"Performing post-processing cleanup (usage: {stats.usage_percent:.1f}%)")
                    memory_manager.force_garbage_collection()
            except Exception as e:
                print(f"Error in memory cleanup: {e}")

        return result_tuple

    # This except block should be indented to match the main 'try' block starting around line 151 (INDENTED)
    except (KeyboardInterrupt, InterruptedError) as e:
        print(f"Optimization interrupted: {e}")
        # Return shutdown message
        empty_fig = go.Figure()
        empty_fig.update_layout(template="plotly_dark")
        return ("Optimization interrupted", empty_fig, empty_fig, False, "", empty_fig, None, None, None)
    except Exception as e:
        print(f"Error in portfolio optimization: {e}")
        # Return default values including clearing the store on error
        empty_fig = go.Figure()
        empty_fig.update_layout(template="plotly_dark")
        # Ensure the return tuple matches the number of outputs (9)
        return ("Error during optimization", empty_fig, empty_fig, False, "", empty_fig, None, None, None) # MPT/CVaR outputs
# End of the update_portfolio_optimization function

if __name__ == "__main__":
    atexit.register(mt5.shutdown)
    
    app.run(debug=True, port=8053, dev_tools_hot_reload=False)
    #serve(app.server, host='0.0.0.0', port=8053, threads=4, _quiet=True)