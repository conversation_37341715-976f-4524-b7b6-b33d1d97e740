{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py313\", \"abi3-py314\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"arc_lock\", \"auto-initialize\", \"bigdecimal\", \"chrono\", \"chrono-local\", \"chrono-tz\", \"default\", \"either\", \"experimental-async\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"jiff-02\", \"lock_api\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"num-rational\", \"ordered-float\", \"parking_lot\", \"py-clone\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"time\", \"unindent\", \"uuid\"]", "target": 1859062398649441551, "profile": 902425061678724816, "path": 8681212485604955630, "deps": [[629381703529241162, "indoc", false, 9107386474321796607], [3722963349756955755, "once_cell", false, 13244492203321078007], [4684437522915235464, "libc", false, 9685844023081248841], [5099523288940447918, "pyo3_ffi", false, 18063455351691512052], [5197680718850464868, "pyo3_macros", false, 4431471064294241926], [9768805234657844767, "build_script_build", false, 7765786138003919192], [14643204177830147187, "memoffset", false, 995327542446860875], [14748792705540276325, "unindent", false, 17135912380718091844]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-df1366de9ba913be\\dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}