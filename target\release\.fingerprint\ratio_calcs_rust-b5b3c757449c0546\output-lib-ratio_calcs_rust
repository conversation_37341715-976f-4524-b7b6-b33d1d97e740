{"$message_type":"diagnostic","message":"unused import: `PyArray1`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":70,"byte_end":78,"line_start":2,"line_end":2,"column_start":49,"column_end":57,"is_primary":true,"text":[{"text":"use numpy::{PyReadonlyArray1, PyReadonlyArray2, PyArray1};","highlight_start":49,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":68,"byte_end":78,"line_start":2,"line_end":2,"column_start":47,"column_end":57,"is_primary":true,"text":[{"text":"use numpy::{PyReadonlyArray1, PyReadonlyArray2, PyArray1};","highlight_start":47,"highlight_end":57}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `PyArray1`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:2:49\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse numpy::{PyReadonlyArray1, PyReadonlyArray2, PyArray1};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Array1`, `Array2`, `ArrayView1`, and `ArrayView2`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":95,"byte_end":101,"line_start":3,"line_end":3,"column_start":15,"column_end":21,"is_primary":true,"text":[{"text":"use ndarray::{Array1, Array2, ArrayView1, ArrayView2};","highlight_start":15,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":103,"byte_end":109,"line_start":3,"line_end":3,"column_start":23,"column_end":29,"is_primary":true,"text":[{"text":"use ndarray::{Array1, Array2, ArrayView1, ArrayView2};","highlight_start":23,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":111,"byte_end":121,"line_start":3,"line_end":3,"column_start":31,"column_end":41,"is_primary":true,"text":[{"text":"use ndarray::{Array1, Array2, ArrayView1, ArrayView2};","highlight_start":31,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":123,"byte_end":133,"line_start":3,"line_end":3,"column_start":43,"column_end":53,"is_primary":true,"text":[{"text":"use ndarray::{Array1, Array2, ArrayView1, ArrayView2};","highlight_start":43,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":81,"byte_end":136,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use ndarray::{Array1, Array2, ArrayView1, ArrayView2};","highlight_start":1,"highlight_end":55},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Array1`, `Array2`, `ArrayView1`, and `ArrayView2`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:3:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse ndarray::{Array1, Array2, ArrayView1, ArrayView2};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"2 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 2 warnings emitted\u001b[0m\n\n"}
